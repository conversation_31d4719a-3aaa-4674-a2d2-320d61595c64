package repository

import (
	"context"
	"time"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type BurstRepository interface {
	IsBursting(ctx context.Context, param CheckBurstParam) (bool, error)
	LockBurstUser(ctx context.Context, lockParams BurstLockParam) error
	GetBurstUserLock(ctx context.Context, lockParam BurstLockParam) (string, error)
	LockBurstSymbol(ctx context.Context, lockParam BurstLockParam) error
	GetUserBurstAllSymbolLock(ctx context.Context, lockParam BurstLockParam) ([]string, error)
	UnlockBurstUser(ctx context.Context, lockParam BurstLockParam, clear bool) error
	GetBurstLockInfo(ctx context.Context, lockParam BurstLockParam) (string, string, error)
	UserScanTryLock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) (bool, error)
	UserScanUnlock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string)
	UserTrialScanTryLock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) (bool, error)
	UserTrialScanUnlock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string)

	// db
	GetBurstInfoByTableNameAndId(ctx context.Context, id string) (*entity.BurstSwap, error)
	SearchBurstInfos(ctx context.Context, conditions map[string]interface{}, ranges map[string]map[string]interface{}, pageNum, pageSize int) (int64, []entity.BurstSwap)
	StatBurstTimesByTableNameList(ctx context.Context, startTime, endTime time.Time) []entity.StatBurstInfo

	TrialUserId(uid string) string
	RemoveUserIdTrial(userId string) string
	UserIdIsContainTrial(userId string) bool
	RivalScoreRate(ctx context.Context, uid, symbol string, posSide int32, isTrialPos bool) decimal.Decimal
	GetUserBurstLevel(ctx context.Context, uid, symbol string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) string
	SaveUserBurstLevel(ctx context.Context, uid, symbol, level string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) error
	RivalScore(ctx context.Context, uid, symbol string, posSide int32, isTrialPos bool) decimal.Decimal
	FetchUserAllRivalScore(ctx context.Context, uid string) (map[string]string, map[string]string)
	RemoveUserAllRivalScore(ctx context.Context, uid string)
	RemoveRivalScore(ctx context.Context, uid, symbol string, posSide int32, isTrial bool)

	GetBurstServerContracts(ctx context.Context) []string
}

type CheckBurstParam struct {
	UID          string
	ContractCode string
	MarginMode   futuresassetpb.MarginMode
	IsTrialPos   bool // 是否体验金仓位
}

type BurstLockParam struct {
	UID          string                    `json:"uid"`
	ContractCode string                    `json:"contract_code"`
	MarginMode   futuresassetpb.MarginMode `json:"margin_mode"`
	BurstId      string                    `json:"burst_id"`
	BurstTime    int64                     `json:"burst_time"`
	PosSide      int32                     `json:"pos_side"`
	IsTrialPos   bool                      `json:"is_trial_pos"` // 是否体验金仓位
}
