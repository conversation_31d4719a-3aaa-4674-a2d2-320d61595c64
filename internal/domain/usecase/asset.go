package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// mockgen -source./asset.go -destination=../../mock/usecase/
//
//nolint:interfacebloat
type AssetUseCase interface {
	UserAsset(ctx context.Context, param *repository.SwapParam) (*repository.AssetSwap, error)
	BatchUserAsset(ctx context.Context, param AssetParam) ([]repository.BatchAssetSwap, error)
	GetUserAssetAndPos(ctx context.Context, param *repository.SwapParam) (domain.Code, repository.ReqUserAssetAndPos)
	SumUserTotalAsset(ctx context.Context) (repository.ResSumUserTotalAsset, error)
	TotalBalance(ctx context.Context, param ReqTotalBalance) (domain.Code, repository.ResTotalBalance)
	AssetDetail(ctx context.Context, param ReqAssetDetail) (repository.ResAssetDetail, error)

	LockAsset(ctx context.Context, param *BatchLockAssetParam) ([]*futuresassetpb.AssetOperationResult, error)
	UnLockAsset(ctx context.Context, param *BatchUnlockParam) (*BatchUnlockReply, error)
}

type AssetParam struct {
	UserIds  []string `json:"userIds"`
	Currency string   `json:"currency"`
}

type ReqTotalBalance struct {
	UID      string `json:"uid"`
	Currency string `json:"currency"` // 折合的币种
}

type ReqAssetDetail struct {
	UID string `json:"uid"`
}

type (
	LockAssetOrder struct {
		OrderID         string          `json:"order_id"`         // 委托单ID
		Amount          decimal.Decimal `json:"amount"`           // 数量
		OrderTime       int64           `json:"order_time"`       // 委托创建时间
		Leverage        int             `json:"leverage"`         // 杠杆倍数
		LiquidationType int             `json:"liquidation_type"` // 强平类型
		AwardIds        []string        `json:"award_ids"`        // 体验金券ID
		AwardUsed       bool            `json:"award_used"`       // 体验金是否使用
	}
	BatchLockAssetParam struct {
		UID          string                      `json:"uid"`           // 用户ID
		UserType     futuresassetpb.UserType     `json:"user_type"`     // 用户类型
		Symbol       string                      `json:"symbol"`        // 币对
		Currency     string                      `json:"currency"`      // 计价币种
		Leverage     int32                       `json:"leverage"`      // 杠杆倍数
		MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 仓位模式 (1:全仓 2:逐仓)
		IsInnerCall  int32                       `json:"is_inner_call"` // 是否为服务内部调用
		PosSide      futuresassetpb.PosSide      `json:"pos_side"`      // 持仓方向
		PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 持仓模式
		Orders       []*LockAssetOrder           `json:"orders"`        // 订单列表
		IsTrial      bool                        `json:"is_trial"`      // 是否体验金冻结
	}
)

type UnLockParam struct {
	UID             string                      `json:"uid"`              // 用户ID
	UserType        futuresassetpb.UserType     `json:"user_type"`        // 用户类型
	OrderId         string                      `json:"order_id"`         // 委托单ID
	Symbol          string                      `json:"symbol"`           // 币对
	Currency        string                      `json:"currency"`         // 计价币种
	Amount          decimal.Decimal             `json:"amount"`           // 数量
	MarginMode      futuresassetpb.MarginMode   `json:"margin_mode"`      // 仓位模式 (1:全仓 2:逐仓)
	IsInnerCall     int32                       `json:"is_inner_call"`    // 是否为服务内部调用下单(爆仓) 1:内部服务下单
	PositionMode    futuresassetpb.PositionMode `json:"position_mode"`    // 持仓模式 (1.双向 2.单向)
	LiquidationType int32                       `json:"liquidation_type"` // 强平类型
	AwardOpIds      []string                    `json:"award_op_ids"`     // 体验金券ID
	TrialIsEnd      bool                        `json:"trial_is_end"`     // 体验金是否成交
}

type BatchUnlockParam struct {
	UID          string                      `json:"uid"`           // 用户ID
	UserType     futuresassetpb.UserType     `json:"user_type"`     // 用户类型
	Symbol       string                      `json:"symbol"`        // 币对
	Currency     string                      `json:"currency"`      // 计价币种
	MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 仓位模式 (1:全仓 2:逐仓)
	PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 持仓模式 (1.双向 2.单向)

	Orders  []*UnLockParam `json:"orders"`
	IsTrial bool           `json:"is_trial"` // 是否体验金冻结
}

type BatchUnlockReply struct {
	AssetSwap   *repository.AssetSwap `json:"asset_swap"`
	SuccessList []*UnLockParam        `json:"success_list"`
	FailedList  []*UnLockParam        `json:"failed_list"`
}
