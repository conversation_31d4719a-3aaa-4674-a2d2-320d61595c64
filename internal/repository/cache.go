package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strconv"
	"strings"

	"futures-asset/cache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type NewCacheRepositoryParam struct {
	dig.In

	DB      *gorm.DB             `name:"db"`
	Redis   *redis.ClusterClient `name:"redis-cluster"`
	RedSync *redsync.Redsync     `name:"rs"`
	Mongo   *mongo.Database      `name:"mongodb"`

	PriceRepo repository.PriceRepository
}

type CacheRepository struct {
	db    *gorm.DB
	rdb   *redis.ClusterClient
	rs    *redsync.Redsync
	mongo *mongo.Database

	priceRepo repository.PriceRepository
}

func NewCacheRepository(param NewCacheRepositoryParam) repository.CacheRepository {
	return &CacheRepository{
		db:    param.DB,
		rdb:   param.Redis,
		rs:    param.RedSync,
		mongo: param.Mongo,

		priceRepo: param.PriceRepo,
	}
}

// GetAllCacheKey implements repository.CacheRepository.
// TODO 验证函数正确性
func (repo *CacheRepository) GetAllCacheKey(ctx context.Context) ([]string, error) {
	var allKeys []string

	for _, node := range repo.rdb.ClusterSlots(ctx).Val() {
		nodeClient := redis.NewClient(&redis.Options{
			Addr: node.Nodes[0].Addr,
		})

		var cursor uint64
		for {
			keys, nextCursor, err := nodeClient.Scan(context.Background(), cursor, domain.AssetPrefix.Key("*"), 20).Result()
			if err != nil {
				return nil, err
			}

			allKeys = append(allKeys, keys...)

			// 如果 nextCursor 为 0，表示已经扫描完该节点所有符合条件的 keys
			if nextCursor == 0 {
				break
			}
			cursor = nextCursor
		}
	}

	return allKeys, nil
}

// GetUserAssetMode implements repository.CacheRepository.
func (repo *CacheRepository) GetUserAssetMode(ctx context.Context, uid string) (futuresassetpb.AssetMode, error) {
	assetMode, err := repo.rdb.HGet(ctx, domain.AssetPrefix.Key(uid), domain.AssetMode.Key()).Int()
	if err != nil {
		return futuresassetpb.AssetMode(0), err
	}
	userAsset := &repository.AssetSwap{
		UID: uid,
	}

	userAsset.AssetMode = futuresassetpb.AssetMode(assetMode)
	if userAsset.AssetMode == 0 {
		userAsset.AssetMode = futuresassetpb.AssetMode_ASSET_MODE_SINGLE
	}

	return userAsset.AssetMode, nil
}

// Load 获取用户资产, 原 Load() 函数
func (repo *CacheRepository) Load(ctx context.Context, uid, pair string) (*repository.AssetSwap, error) {
	crossPos, isolatedPos := make(map[string][]repository.PosSwap), make(map[string][]repository.PosSwap)
	posStrList, err := repo.rdb.HGetAll(ctx, domain.AssetPrefix.Key(uid)).Result()
	if err != nil {
		return nil, err
	}

	for subKey, posStr := range posStrList {
		if strings.Contains(subKey, cache.PosFlag) {
			pos := repository.PosSwap{}
			err := json.Unmarshal([]byte(posStr), &pos)
			if err != nil {
				continue
			}
			if pos.Isolated() {
				isolated, ok := isolatedPos[strings.ToUpper(pos.ContractCode)]
				if !ok {
					isolated = make([]repository.PosSwap, 0)
				}
				isolated = append(isolated, pos)
				isolatedPos[strings.ToUpper(pos.ContractCode)] = isolated
			} else {
				cross, ok := crossPos[strings.ToUpper(pos.ContractCode)]
				if !ok {
					cross = make([]repository.PosSwap, 0)
				}
				cross = append(cross, pos)
				crossPos[strings.ToUpper(pos.ContractCode)] = cross
			}
		}
	}

	userAsset := &repository.AssetSwap{
		UID:          uid,
		Balance:      make(map[string]decimal.Decimal),
		Leverage:     make([]*repository.Leverage, 0),
		Frozen:       make(map[string]decimal.Decimal),
		CrossList:    crossPos,
		IsolatedList: isolatedPos,
	}
	leverage, ok := posStrList[domain.Leverage.Key()]
	if ok {
		err := json.Unmarshal([]byte(leverage), &userAsset.Leverage)
		if err != nil {
			return nil, err
		}
	}
	frozen, ok := posStrList[domain.Frozen.Key()]
	if ok {
		err = json.Unmarshal([]byte(frozen), &userAsset.Frozen)
		if err != nil {
			return nil, err
		}
	}
	if userAsset.Frozen == nil {
		userAsset.Frozen = make(map[string]decimal.Decimal)
	}
	balance, ok := posStrList[domain.Balance.Key()]
	if ok {
		err = json.Unmarshal([]byte(balance), &userAsset.Balance)
		if err != nil {
			parsedBalance, err := decimal.NewFromString(balance)
			if parsedBalance.GreaterThan(decimal.Zero) {
				userAsset.Balance = map[string]decimal.Decimal{
					"USDT": parsedBalance,
				}
				repo.UpdateBalance(ctx, userAsset)
			}
			if err != nil {
				return nil, err
			}
		}
	}

	// if len(slf.LongPosKey) != 0 {
	// 	longPos, ok := posStrList[slf.LongPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(longPos), &userAsset.LongPos)
	// 		if err != nil {
	// 			log.Printf("*get user long pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.LongPos.PosSide == 0 {
	// 		// log.Printf("New long pos open uid:%s base:%s quote:%s", slf.UID, slf.Base, slf.Quote)
	// 		userAsset.LongPos.Init(slf.Base, slf.Quote, slf.UID, domain.LongPos)
	// 	}
	// }
	// if len(slf.ShortPosKey) != 0 {
	// 	shortPos, ok := posStrList[slf.ShortPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(shortPos), &userAsset.ShortPos)
	// 		if err != nil {
	// 			log.Printf("*get user short pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.ShortPos.PosSide == 0 {
	// 		// log.Printf("New short pos open uid:%s base:%s quote:%s", slf.UID, slf.Base, slf.Quote)
	// 		userAsset.ShortPos.Init(slf.Base, slf.Quote, slf.UID, domain.ShortPos)
	// 	}
	// }
	// if len(slf.BothPosKey) != 0 {
	// 	bothPos, ok := posStrList[slf.BothPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(bothPos), &userAsset.BothPos)
	// 		if err != nil {
	// 			log.Printf("*get user both pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.BothPos.PosSide == 0 {
	// 		userAsset.BothPos.Init(slf.Base, slf.Quote, slf.UID, domain.BothPos)
	// 	}
	// }

	// // 体验金仓位
	// if slf.TrialLongPosKey != "" {
	// 	trialLongPos, ok := posStrList[slf.TrialLongPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(trialLongPos), &userAsset.TrialLongPos)
	// 		if err != nil {
	// 			log.Printf("*get user trial long pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.TrialLongPos.PosSide == 0 {
	// 		userAsset.TrialLongPos.Init(slf.Base, slf.Quote, slf.UID, domain.LongPos)
	// 	}
	// }
	// if slf.TrialShortPosKey != "" {
	// 	trialShortPos, ok := posStrList[slf.TrialShortPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(trialShortPos), &userAsset.TrialShortPos)
	// 		if err != nil {
	// 			log.Printf("*get user trial short pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.TrialShortPos.PosSide == 0 {
	// 		userAsset.TrialShortPos.Init(slf.Base, slf.Quote, slf.UID, domain.ShortPos)
	// 	}
	// }
	// if slf.TrialBothPosKey != "" {
	// 	trialBothPos, ok := posStrList[slf.TrialBothPosKey]
	// 	if ok {
	// 		err = json.Unmarshal([]byte(trialBothPos), &userAsset.TrialBothPos)
	// 		if err != nil {
	// 			log.Printf("*get user trial both pos err %s*", slf.UID)
	// 			return nil, err
	// 		}
	// 	}
	// 	if userAsset.TrialBothPos.PosSide == 0 {
	// 		userAsset.TrialBothPos.Init(slf.Base, slf.Quote, slf.UID, domain.BothPos)
	// 	}
	// }

	// 读取体验金详情
	tDetail, ok := posStrList[domain.TrialDetail.Key()]
	if ok {
		if tDetail == "null" {
			tDetail = "[]"
		}
		err = json.Unmarshal([]byte(tDetail), &userAsset.TrialDetail)
		if err != nil {
			oldTrial := repository.TrialAsset{}
			err = json.Unmarshal([]byte(tDetail), &oldTrial)
			if err != nil {
				log.Printf("*get user trial asset err %s*", userAsset.UID)
				return nil, err
			}
			userAsset.TrialDetail = oldTrial.TrialList
			if len(oldTrial.TrialList) > 0 {
				trialBalanceData := make(map[string]decimal.Decimal, len(domain.CurrencyList))
				for i := range domain.CurrencyList {
					trialBalanceData[domain.CurrencyList[i]] = decimal.Zero
				}
				for _, trialInfo := range oldTrial.TrialList {
					trialBalanceData[trialInfo.Currency] = trialBalanceData[trialInfo.Currency].Add(trialInfo.AwardAmount.Sub(trialInfo.AmountUsed))
				}
				userAsset.TrialBalance = trialBalanceData
			}
		}
		// 检查失效体验金
		sort.Sort(userAsset.TrialDetail)

		trialBalanceData := make(map[string]decimal.Decimal, len(domain.CurrencyList))
		for i := range domain.CurrencyList {
			trialBalanceData[domain.CurrencyList[i]] = decimal.Zero
		}

		trialConsumeData := make(map[string]decimal.Decimal, len(domain.CurrencyList))
		for i := range domain.CurrencyList {
			trialConsumeData[domain.CurrencyList[i]] = decimal.Zero
		}

		for i, trialInfo := range userAsset.TrialDetail {
			// 用0占位
			trialInfo.OpType = domain.BillTypeAll

			switch trialInfo.TrialAssetStatus {
			case domain.TrialAssetStatusEffect, domain.TrialAssetStatusWarn:
				if trialInfo.AmountUsed.LessThan(trialInfo.AwardAmount) {
					trialBalanceData[trialInfo.Currency] = trialBalanceData[trialInfo.Currency].Add(trialInfo.AwardAmount.Sub(trialInfo.AmountUsed))
					trialConsumeData[trialInfo.Currency] = trialConsumeData[trialInfo.Currency].Add(trialInfo.AmountUsed)
				}
			default:
			}

			trialInfo.RecycleAmount = trialInfo.AwardAmount.Sub(trialInfo.AmountUsed).Sub(trialInfo.RecoveryAmount)
			if trialInfo.RecycleAmount.LessThan(decimal.Zero) {
				trialInfo.RecycleAmount = decimal.Zero
			}

			userAsset.TrialDetail[i] = trialInfo
		}

		userAsset.TrialBalance = trialBalanceData
		userAsset.TrialConsume = trialConsumeData
	} else {
		userAsset.TrialDetail = repository.TrialTimeList{}
		trialBalanceData := make(map[string]decimal.Decimal, len(domain.CurrencyList))
		for i := range domain.CurrencyList {
			trialBalanceData[domain.CurrencyList[i]] = decimal.Zero
		}
		userAsset.TrialBalance = trialBalanceData

		trialConsumeData := make(map[string]decimal.Decimal, len(domain.CurrencyList))
		for i := range domain.CurrencyList {
			trialConsumeData[domain.CurrencyList[i]] = decimal.Zero
		}
		userAsset.TrialConsume = trialConsumeData
	}

	positionMode, ok := posStrList[domain.PositionMode.Key()]
	if ok {
		positionModeInt, _ := strconv.Atoi(positionMode)
		userAsset.PositionMode = futuresassetpb.PositionMode(positionModeInt)
		if userAsset.PositionMode == 0 {
			userAsset.PositionMode = futuresassetpb.PositionMode_POSITION_MODE_HEDGE
		}
	} else {
		userAsset.PositionMode = futuresassetpb.PositionMode_POSITION_MODE_HEDGE
	}

	assetMode, ok := posStrList[domain.AssetMode.Key()]
	if ok {
		assetModeInt, _ := strconv.Atoi(assetMode)
		userAsset.AssetMode = futuresassetpb.AssetMode(assetModeInt)
		if userAsset.AssetMode == 0 {
			userAsset.AssetMode = futuresassetpb.AssetMode_ASSET_MODE_SINGLE
		}
	} else {
		userAsset.AssetMode = futuresassetpb.AssetMode_ASSET_MODE_SINGLE
	}
	if userAsset.TrialRecovery == nil {
		userAsset.TrialRecovery = repository.BalanceAsset{}
	}
	if userAsset.TrialLoss == nil {
		userAsset.TrialLoss = repository.BalanceAsset{}
	}

	return userAsset, nil
}

// GetUserPositions 获取用户仓位, 原 CacheUserPos() 函数
func (repo *CacheRepository) GetUserPositions(ctx context.Context, uid, pair string) (map[string][]repository.PosSwap, map[string][]repository.PosSwap, error) {
	crossPosMap, isolatedPosMap := make(map[string][]repository.PosSwap), make(map[string][]repository.PosSwap)
	posStrList, err := repo.rdb.HGetAll(ctx, domain.AssetPrefix.Key(uid)).Result()
	if err != nil {
		log.Printf("[CacheUserPos] call redis HGetAll err:%s, hashkey:%s", err, domain.AssetPrefix.Key(uid))
		return crossPosMap, isolatedPosMap, err
	}

	marketPriceMap := repo.priceRepo.GetAllMarkPrice(ctx)
	for subKey, posStr := range posStrList {
		if strings.Contains(subKey, cache.PosFlag) {
			pos := repository.PosSwap{}
			err := json.Unmarshal([]byte(posStr), &pos)
			if err != nil {
				continue
			}
			if pair != "" && pos.ContractCode != pair {
				continue
			}

			if price, ok := marketPriceMap.Get(pos.ContractCode); ok {
				pos.MarkPrice = price
			}

			if pos.Pos.IsPositive() {
				pos.ProfitUnreal = pos.MarkPrice.Sub(pos.OpenPriceAvg).Mul(pos.Pos.Abs())
			} else {
				pos.ProfitUnreal = pos.OpenPriceAvg.Sub(pos.MarkPrice).Mul(pos.Pos.Abs())
			}

			if pos.Isolated() {
				isolated := isolatedPosMap[strings.ToUpper(pos.ContractCode)]
				isolated = append(isolated, pos)
				isolatedPosMap[strings.ToUpper(pos.ContractCode)] = isolated
			} else {
				cross := crossPosMap[strings.ToUpper(pos.ContractCode)]
				cross = append(cross, pos)
				crossPosMap[strings.ToUpper(pos.ContractCode)] = cross
			}
		}
	}

	return crossPosMap, isolatedPosMap, nil
}

// InitUser implements repository.CacheRepository.
func (repo *CacheRepository) InitUser(ctx context.Context, uid string, leverage []*repository.Leverage) error {
	balance := make(map[string]decimal.Decimal, len(domain.CurrencyList))
	for i := range domain.CurrencyList {
		balance[domain.CurrencyList[i]] = decimal.Zero
	}

	trialBalance := make(map[string]decimal.Decimal, len(domain.CurrencyList))
	for i := range domain.CurrencyList {
		trialBalance[domain.CurrencyList[i]] = decimal.Zero
	}

	balanceStr, _ := json.Marshal(balance)
	trialBalanceStr, _ := json.Marshal(trialBalance)
	leverageStr, _ := json.Marshal(leverage)
	fields := map[string]interface{}{
		domain.Leverage.Key():     leverageStr,
		domain.PositionMode.Key(): strconv.Itoa(int(futuresassetpb.PositionMode_POSITION_MODE_HEDGE)), // 双向持仓模式
		domain.AssetMode.Key():    strconv.Itoa(int(futuresassetpb.AssetMode_ASSET_MODE_SINGLE)),      // 单币种保证金
		domain.Balance.Key():      balanceStr,
		domain.TrialBalance.Key(): trialBalanceStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(uid), fields).Err()
}

// FixInitUser implements repository.CacheRepository.
func (repo *CacheRepository) FixInitUser(ctx context.Context, uid string, leverage map[string]*repository.Leverage) error {
	leverageStr, _ := json.Marshal(leverage)
	fields := map[string]interface{}{}
	positionMode, _ := repo.rdb.HGet(ctx, domain.AssetPrefix.Key(uid), domain.PositionMode.Key()).Result()
	if positionMode == "" {
		fields[domain.PositionMode.Key()] = strconv.Itoa(int(futuresassetpb.PositionMode_POSITION_MODE_HEDGE))
	}
	assetMode, _ := repo.rdb.HGet(ctx, domain.AssetPrefix.Key(uid), domain.AssetMode.Key()).Result()
	if assetMode == "" {
		fields[domain.AssetMode.Key()] = strconv.Itoa(int(futuresassetpb.AssetMode_ASSET_MODE_SINGLE))
	}
	leverageOld, _ := repo.rdb.HGet(ctx, domain.AssetPrefix.Key(uid), domain.Leverage.Key()).Result()
	if leverageOld == "" {
		fields[domain.Leverage.Key()] = string(leverageStr)
	} else {
		info := make([]*repository.Leverage, 0)
		err := json.Unmarshal([]byte(leverageOld), &info)
		if err != nil {
			logrus.Errorf("unmarsha1 leverageOld:%s, err:%v", leverageOld, err)
			return err
		}
		for i := range leverage {
			for j := range info {
				if leverage[i].ContractCode == info[j].ContractCode {
					leverage[i].MarginMode = info[j].MarginMode
					leverage[i].Leverage = info[j].Leverage
					leverage[i].LLeverage = info[j].LLeverage
					leverage[i].SLeverage = info[j].SLeverage
					leverage[i].BLeverage = info[j].BLeverage
				}
			}
		}
		leverageStr, _ := json.Marshal(leverage)
		fields[domain.Leverage.Key()] = string(leverageStr)
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(uid), fields).Err()
}

// UpdateAssetMode 更新资产模式 原 UpdateAssetMode
func (repo *CacheRepository) UpdateAssetMode(ctx context.Context, uid string, mode futuresassetpb.AssetMode) error {
	assetModeStr := strconv.Itoa(int(mode))
	frozenStr, _ := json.Marshal(map[string]decimal.Decimal{})
	fields := map[string]interface{}{
		domain.AssetMode.Key(): assetModeStr,
		domain.Frozen.Key():    frozenStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(uid), fields).Err()
}

// UpdatePositionMode 更新持仓模式 原 UpdatePositionMode
func (repo *CacheRepository) UpdatePositionMode(ctx context.Context, uid string, posMode futuresassetpb.PositionMode) error {
	posModeStr := strconv.Itoa(int(posMode))
	frozenStr, _ := json.Marshal(map[string]decimal.Decimal{})
	fields := map[string]interface{}{
		domain.PositionMode.Key(): posModeStr,
		domain.Frozen.Key():       frozenStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(uid), fields).Err()
}

// UpdateBalance 更新资产
func (repo *CacheRepository) UpdateBalance(ctx context.Context, asset *repository.AssetSwap) error {
	frozen, _ := json.Marshal(asset.Frozen)
	balanceStr, _ := json.Marshal(asset.Balance)
	fields := map[string]interface{}{
		domain.Balance.Key(): balanceStr,
		domain.Frozen.Key():  frozen,
	}

	err := repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
	if err != nil {
		msg := fmt.Sprintf("update balance hmset hash %s err: %v", domain.AssetPrefix.Key(asset.UID), err)
		return errors.New(msg)
	}

	return nil
}

// UpdatePosAndAsset implements repository.CacheRepository.
func (repo *CacheRepository) UpdatePosAndAsset(ctx context.Context, asset *repository.AssetSwap) error {
	shortPosStr, _ := json.Marshal(asset.ShortPos)
	longPosStr, _ := json.Marshal(asset.LongPos)
	bothPosStr, _ := json.Marshal(asset.BothPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	fields := map[string]interface{}{
		domain.Balance.Key(): balance,
		domain.Frozen.Key():  frozen,
		fmt.Sprintf("%s%s", asset.LongPos.ContractCode, domain.LongPosSuffix):   longPosStr,
		fmt.Sprintf("%s%s", asset.ShortPos.ContractCode, domain.ShortPosSuffix): shortPosStr,
		fmt.Sprintf("%s%s", asset.BothPos.ContractCode, domain.BothPosSuffix):   bothPosStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// UpdateBothPos implements repository.CacheRepository.
func (repo *CacheRepository) UpdateBothPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.BothPos)
	balanceStr, _ := json.Marshal(asset.Balance)
	frozenStr, _ := json.Marshal(asset.Frozen)
	trialBalanceStr, _ := json.Marshal(asset.TrialBalance)
	trialConsumeStr, _ := json.Marshal(asset.TrialConsume)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetailStr, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		fmt.Sprintf("%s%s", pair, domain.BothPosSuffix): posStr,
		domain.Balance.Key():                            balanceStr,
		domain.Frozen.Key():                             frozenStr,
		domain.TrialBalance.Key():                       trialBalanceStr,
		domain.TrialConsume.Key():                       trialConsumeStr,
		domain.TrialDetail.Key():                        trialDetailStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// UpdateLeverage implements repository.CacheRepository.
func (repo *CacheRepository) UpdateLeverage(ctx context.Context, uid string, leverage []*repository.Leverage) error {
	leverageStr, _ := json.Marshal(leverage)
	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(uid), domain.Leverage.Key(), string(leverageStr)).Err()
	if err != nil {
		msg := fmt.Sprintf("hset user both pos err: %v", err)
		return errors.New(msg)
	}

	return nil
}

// UpdateLongPos implements repository.CacheRepository.
func (repo *CacheRepository) UpdateLongPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.LongPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	trialBalance, _ := json.Marshal(asset.TrialBalance)
	trialConsume, _ := json.Marshal(asset.TrialConsume)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetail, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		fmt.Sprintf("%s%s", pair, domain.LongPosSuffix): posStr,
		domain.Balance.Key():                            balance,
		domain.Frozen.Key():                             frozen,
		domain.TrialBalance.Key():                       trialBalance,
		domain.TrialConsume.Key():                       trialConsume,
		domain.TrialDetail.Key():                        trialDetail,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// UpdatePos implements repository.CacheRepository.
func (repo *CacheRepository) UpdatePos(ctx context.Context, pair string, asset *repository.AssetSwap, trial bool) error {
	longStr, _ := json.Marshal(asset.LongPos)
	shortStr, _ := json.Marshal(asset.ShortPos)
	bothStr, _ := json.Marshal(asset.BothPos)
	fields := map[string]interface{}{
		fmt.Sprintf("%s%s", pair, domain.LongPosSuffix):  longStr,
		fmt.Sprintf("%s%s", pair, domain.ShortPosSuffix): shortStr,
		fmt.Sprintf("%s%s", pair, domain.BothPosSuffix):  bothStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// UpdateShortPos implements repository.CacheRepository.
func (repo *CacheRepository) UpdateShortPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.ShortPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	trialBalance, _ := json.Marshal(asset.TrialBalance)
	trialConsume, _ := json.Marshal(asset.TrialConsume)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetail, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		fmt.Sprintf("%s%s", pair, domain.ShortPosSuffix): posStr,
		domain.Balance.Key():                             balance,
		domain.Frozen.Key():                              frozen,
		domain.TrialBalance.Key():                        trialBalance,
		domain.TrialConsume.Key():                        trialConsume,
		domain.TrialDetail.Key():                         trialDetail,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// HoldCostTrialTotal 所有币对的体验金成本 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *CacheRepository) HoldCostTrialTotal(ctx context.Context, uid string, assetMode futuresassetpb.AssetMode, area string) decimal.Decimal {
	return decimal.Zero
}

// CrossTrialUnrealTotal 全部全仓的未实现盈亏 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *CacheRepository) CrossTrialUnrealTotal(ctx context.Context, uid string, assetMode futuresassetpb.AssetMode, area string) decimal.Decimal {
	return decimal.Zero
}

// SetPos set positions
func (repo *CacheRepository) SetPos(ctx context.Context, side futuresassetpb.PosSide, pos repository.PosSwap, trial bool) error {
	posStr, _ := json.Marshal(pos)
	var subKey string
	if side == futuresassetpb.PosSide_POS_SIDE_LONG {
		if trial {
			subKey = domain.TrialAssetPrefix.Key(fmt.Sprintf("%s%s", pos.ContractCode, domain.LongPosSuffix))
		} else {
			subKey = fmt.Sprintf("%s%s", pos.ContractCode, domain.LongPosSuffix)
		}
	} else if side == futuresassetpb.PosSide_POS_SIDE_SHORT {
		if trial {
			subKey = domain.TrialAssetPrefix.Key(fmt.Sprintf("%s%s", pos.ContractCode, domain.ShortPosSuffix))
		} else {
			subKey = fmt.Sprintf("%s%s", pos.ContractCode, domain.ShortPosSuffix)
		}
	} else {
		if trial {
			subKey = domain.TrialAssetPrefix.Key(fmt.Sprintf("%s%s", pos.ContractCode, domain.BothPosSuffix))
		} else {
			subKey = fmt.Sprintf("%s%s", pos.ContractCode, domain.BothPosSuffix)
		}
	}

	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(pos.UID), subKey, string(posStr)).Err()
	if err != nil {
		return fmt.Errorf("hset user pos err: %v", err)
	}

	return nil
}

// SetLongPos set long positions
func (repo *CacheRepository) SetLongPos(ctx context.Context, pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(pos.UID), fmt.Sprintf("%s%s", pos.ContractCode, domain.LongPosSuffix), string(posStr)).Err()
	if err != nil {
		msg := fmt.Sprintf("hset user long pos err: %v", err)
		return errors.New(msg)
	}

	return nil
}

// SetShortPos init short positions
func (repo *CacheRepository) SetShortPos(ctx context.Context, pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(pos.UID), fmt.Sprintf("%s%s", pos.ContractCode, domain.ShortPosSuffix), string(posStr)).Err()
	if err != nil {
		msg := fmt.Sprintf("hset user short pos err: %v", err)
		return errors.New(msg)
	}

	return nil
}

// SetBothPos init both positions
func (repo *CacheRepository) SetBothPos(ctx context.Context, pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(pos.UID), fmt.Sprintf("%s%s", pos.ContractCode, domain.BothPosSuffix), string(posStr)).Err()
	if err != nil {
		msg := fmt.Sprintf("hset user both pos err: %v", err)
		return errors.New(msg)
	}

	return nil
}

// SetLeverage init both positions
func (repo *CacheRepository) SetLeverage(ctx context.Context, uid string, leverage map[string]*repository.Leverage) error {
	leverageStr, _ := json.Marshal(leverage)
	err := repo.rdb.HSet(ctx, domain.AssetPrefix.Key(uid), domain.Leverage.Key(), string(leverageStr)).Err()
	if err != nil {
		msg := fmt.Sprintf("hset user both pos err: %v", err)
		return errors.New(msg)
	}

	return nil
}

// UpdateAnyPos 更新任意仓位
func (repo *CacheRepository) UpdateAnyPos(ctx context.Context, pos repository.PosSwap) error {
	posKey := ""
	switch pos.PosSide {
	case domain.LongPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.LongPosSuffix)

	case domain.ShortPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.ShortPosSuffix)

	case domain.BothPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.BothPosSuffix)

	default:
		return nil
	}

	posStr, _ := json.Marshal(pos)
	fields := map[string]interface{}{
		posKey: posStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(pos.UID), fields).Err()
}
