package utils

import (
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

type AccountSettleParam struct {
	AccountSettle *futuresEnginePB.AccountSettleEngine
}

// GetUID 从AccountSettleEngine中提取用户ID
func (p *AccountSettleParam) GetUID() string {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetUID: accountSettle or accountSettle.Order is nil")
		return ""
	}

	uid := p.AccountSettle.Order.UserId
	logrus.Infof("GetUID: extracted UID=%s", uid)
	return uid
}

// GetAmount 从AccountSettleEngine中提取交易数量
func (p *AccountSettleParam) GetAmount() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("GetAmount: accountSettle is nil")
		return decimal.Zero
	}

	amount, err := decimal.NewFromString(p.AccountSettle.Amount)
	if err != nil {
		logrus.Warnf("GetAmount: failed to parse amount %s: %v", p.AccountSettle.Amount, err)
		return decimal.Zero
	}
	logrus.Infof("GetAmount: extracted amount=%s", amount.String())
	return amount
}

// GetPrice 从AccountSettleEngine中提取价格
func (p *AccountSettleParam) GetPrice() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("GetPrice: accountSettle is nil")
		return decimal.Zero
	}

	price, err := decimal.NewFromString(p.AccountSettle.Price)
	if err != nil {
		logrus.Warnf("GetPrice: failed to parse price %s: %v", p.AccountSettle.Price, err)
		return decimal.Zero
	}
	logrus.Infof("GetPrice: extracted price=%s", price.String())
	return price
}

// GetBaseQuote 从AccountSettleEngine中提取交易对信息
func (p *AccountSettleParam) GetBaseQuote() (string, string) {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetBaseQuote: accountSettle or accountSettle.Order is nil")
		return "", ""
	}

	base, quote := p.AccountSettle.Order.Symbol.Base, p.AccountSettle.Order.Symbol.Quote
	logrus.Infof("GetBaseQuote: extracted base=%s, quote=%s", base, quote)
	return base, quote
}

// IsOpenPosition 判断是否为开仓操作
func (p *AccountSettleParam) IsOpenPosition() bool {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("IsOpenPosition: accountSettle or accountSettle.Order is nil")
		return false
	}

	side := p.AccountSettle.Order.Side
	posSide := p.AccountSettle.Order.PosSide

	// 买入多仓或卖出空仓为开仓
	return (side == domain.Buy && posSide == domain.Long) ||
		(side == domain.Sell && posSide == domain.Short)
}

// GetSide 从AccountSettleEngine中提取交易方向
func (p *AccountSettleParam) GetSide() int {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetSide: accountSettle or accountSettle.Order is nil")
		return 0
	}

	side := int(p.AccountSettle.Order.Side)
	logrus.Infof("GetSide: extracted side=%d", side)
	return side
}

// GetUserType 从AccountSettleEngine中提取用户类型
func (p *AccountSettleParam) GetUserType() int32 {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetUserType: accountSettle or accountSettle.Order is nil")
		return 0
	}

	userType := int32(p.AccountSettle.Order.UserType)
	logrus.Infof("GetUserType: extracted userType=%d", userType)
	return userType
}

// GetMarginMode 从AccountSettleEngine中提取保证金模式
func (p *AccountSettleParam) GetMarginMode() domain.MarginMode {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetMarginMode: accountSettle or accountSettle.Order is nil")
		return domain.MarginModeNone
	}

	marginMode := domain.MarginMode(p.AccountSettle.Order.MarginMode)
	logrus.Infof("GetMarginMode: extracted marginMode=%d", marginMode)
	return marginMode
}

// GetLeverage 从AccountSettleEngine中提取杠杆倍数
func (p *AccountSettleParam) GetLeverage() int {
	if p.AccountSettle == nil || p.AccountSettle.Order == nil {
		logrus.Warn("GetLeverage: accountSettle or accountSettle.Order is nil")
		return 1
	}

	leverage := int(p.AccountSettle.Order.Leverage)
	logrus.Infof("GetLeverage: extracted leverage=%d", leverage)
	return leverage
}

// GetUnfrozenMargin 从AccountSettleEngine中提取解冻保证金
func (p *AccountSettleParam) GetUnfrozenMargin() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("GetUnfrozenMargin: accountSettle is nil")
		return decimal.Zero
	}

	// Todo 需要james添加字段
	unfrozenMargin, err := decimal.NewFromString("p.AccountSettle.UnfrozenMargin")
	if err != nil {
		logrus.Warnf("GetUnfrozenMargin: failed to parse unfrozenMargin %s: %v", "p.AccountSettle.UnfrozenMargin", err)
		return decimal.Zero
	}
	logrus.Infof("GetUnfrozenMargin: extracted unfrozenMargin=%s", unfrozenMargin.String())
	return unfrozenMargin
}

// GetFee 从AccountSettleEngine中提取手续费率
func (p *AccountSettleParam) GetFeeRate() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("GetFeeRate: accountSettle is nil")
		return decimal.Zero
	}

	feeRate, err := decimal.NewFromString(p.AccountSettle.FeedRate)
	if err != nil {
		logrus.Warnf("GetFeeRate: failed to parse feeRate %s: %v", p.AccountSettle.FeedRate, err)
		return decimal.Zero
	}
	logrus.Infof("GetFeeRate: extracted feeRate=%s", feeRate.String())
	return feeRate
}

// GetLiquidationType 从AccountSettleEngine中提取清算类型
func (p *AccountSettleParam) GetLiquidationType() domain.LiquidationType {
	if p.AccountSettle == nil {
		logrus.Warn("GetLiquidationType: accountSettle is nil")
		return domain.LiquidationTypeNone
	}

	liquidationType := domain.LiquidationType(p.AccountSettle.LiquidationType)
	logrus.Infof("GetLiquidationType: extracted liquidationType=%d", liquidationType)
	return liquidationType
}
