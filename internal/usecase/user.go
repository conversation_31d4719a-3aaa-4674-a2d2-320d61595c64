package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// UserUseCase 用户用例实现
type UserUseCase struct {
	config   *cfg.Config[configs.Config]
	userRepo repository.UserRepository
}

// UserUseCaseParam 用户用例参数
type UserUseCaseParam struct {
	dig.In

	Config   *cfg.Config[configs.Config] `name:"config"`
	UserRepo repository.UserRepository
}

// NewUserUseCase 创建用户用例实例
func NewUserUseCase(param UserUseCaseParam) usecase.UserUseCase {
	return &UserUseCase{
		config:   param.Config,
		userRepo: param.UserRepo,
	}
}

// AdjustCross implements usecase.UserUseCase.
func (use *UserUseCase) AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error) {
	return use.userRepo.AdjustCross(ctx, p)
}

// AdjustHoldMode implements usecase.UserUseCase.
func (use *UserUseCase) AdjustHoldMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error) {
	return use.userRepo.AdjustHoldMode(ctx, p)
}

// AdjustLeverage implements usecase.UserUseCase.
func (use *UserUseCase) AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, error) {
	return use.userRepo.AdjustLeverage(ctx, p)
}

// AdjustLeverageMargin implements usecase.UserUseCase.
func (use *UserUseCase) AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error) {
	return use.userRepo.AdjustLeverageMargin(ctx, p)
}

// AdjustMargin implements usecase.UserUseCase.
func (use *UserUseCase) AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error) {
	return use.userRepo.AdjustMargin(ctx, p)
}

// AdjustMarginCfg implements usecase.UserUseCase.
func (use *UserUseCase) AdjustMarginCfg(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error) {
	return use.userRepo.AdjustMarginCfg(ctx, p)
}

// ChangeJoinMargin implements usecase.UserUseCase.
func (use *UserUseCase) ChangeJoinMargin(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error) {
	return use.userRepo.ChangeJoinMargin(ctx, p)
}

// UpdateOrderConfig 修改订单配置
func (use *UserUseCase) UpdateOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error) {
	return use.userRepo.ChangeOrderConfig(ctx, p)
}

// UpdateOrderConfirm 修改下单确认
func (use *UserUseCase) UpdateOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error) {
	return use.userRepo.UpdateOrderConfirm(ctx, p)
}

// LoadJoinMargin 获取参与保证金
func (use *UserUseCase) LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error) {
	return use.userRepo.LoadJoinMargin(ctx, param)
}

// GetUserAsset 获取用户资产
func (use *UserUseCase) GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset) {
	return use.userRepo.GetUserAsset(ctx, param)
}

// SwapInit 初始化合约账户
func (use *UserUseCase) SwapInit(ctx context.Context, param *repository.UIDParam) domain.Code {
	return use.userRepo.SwapInit(ctx, param)
}

// UserLeverage 获取杠杆倍数
func (use *UserUseCase) UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error) {
	return use.userRepo.UserLeverage(ctx, param)
}

func (use *UserUseCase) UserBasicConfig(ctx context.Context, param *repository.UIDParam) (*repository.UserBasicConfig, error) {
	return use.userRepo.UserBasicConfig(ctx, param)
}

// GetUserOpenCloseTimes 获取开仓次数
func (use *UserUseCase) GetUserOpenCloseTimes(ctx context.Context, param *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error) {
	return use.userRepo.GetUserOpenCloseTimes(ctx, param)
}

// GetUserStatistics 获取用户统计
func (use *UserUseCase) GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error) {
	return use.userRepo.GetUserStatistics(ctx, param)
}
