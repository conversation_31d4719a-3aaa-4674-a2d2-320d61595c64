package handler

import (
	"context"

	"futures-asset/internal/delivery/grpc/response"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	"yt.com/backend/common.git/transport/grpcinit"
)

type NewAssetHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
	BurstRepo    repository.BurstRepository
}

type AssetHandler struct {
	assetUseCase usecase.AssetUseCase
	burstRepo    repository.BurstRepository

	futuresassetpb.FuturesAssetServiceServer
}

func NewAssetHandler(param NewAssetHandlerParam) *AssetHandler {
	return &AssetHandler{
		assetUseCase: param.AssetUseCase,
		burstRepo:    param.BurstRepo,
	}
}

func (h *AssetHandler) LockAsset(ctx context.Context, req *futuresassetpb.BatchLockAssetRequest) (*futuresassetpb.BatchAssetOperationResponse, error) {
	orders := make([]*payload.LockAssetOrder, 0, len(req.Orders))
	for _, order := range req.Orders {
		amount, _ := decimal.NewFromString(order.Amount)
		orders = append(orders, &payload.LockAssetOrder{
			OrderID:   order.OrderId,
			Amount:    amount,
			OrderTime: order.OrderTime,
			AwardIds:  order.AwardIds,
			AwardUsed: order.AwardUsed,
		})
	}

	param := &payload.BatchLockAssetParam{
		UID:          req.Uid,
		UserType:     req.UserType,
		Symbol:       req.Symbol,
		Currency:     req.Currency,
		Leverage:     req.Leverage,
		IsInnerCall:  req.IsInnerCall,
		PosSide:      req.PosSide,
		PositionMode: req.PositionMode,
		MarginMode:   req.MarginMode,
		Orders:       orders,
		IsTrial:      req.IsTrial,
	}

	reply := &futuresassetpb.BatchAssetOperationResponse{
		Uid:        req.Uid,
		Symbol:     req.Symbol,
		ResultCode: futuresassetpb.BatchResultCode_ALL_FAILED,
	}

	items, err := h.assetUseCase.LockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		return reply, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	reply.ResultCode = futuresassetpb.BatchResultCode_ALL_SUCCESS
	reply.Results = items
	for _, item := range reply.Results {
		if !item.Success {
			reply.ResultCode = futuresassetpb.BatchResultCode_PARTIAL_SUCCESS
			break
		}
	}

	return reply, nil
}

func (h *AssetHandler) UnLockAsset(ctx context.Context, req *futuresassetpb.BatchUnLockAssetRequest) (*futuresassetpb.BatchAssetOperationResponse, error) {
	param := &payload.BatchUnlockParam{
		UID:          req.Uid,
		UserType:     req.UserType,
		Symbol:       req.Symbol,
		Currency:     req.Currency,
		MarginMode:   req.MarginMode,
		PositionMode: req.PositionMode,
		Orders:       []*payload.UnLockParam{},
		IsTrial:      req.IsTrial,
	}
	_, err := h.assetUseCase.UnLockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.BatchAssetOperationResponse{}, nil
}

// func (h *AssetHandler) GetUserAsset(ctx context.Context, req *futuresassetpb.) (*futuresassetpb.LockAssetResponse, error) {
// 	return nil, nil
// }
