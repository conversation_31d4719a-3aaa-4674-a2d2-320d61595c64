package payload

import (
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type (
	UIDParam struct {
		UID string `json:"uid" binding:"required"` // 用户ID
	}
	MarginParam struct {
		UID          string                    `json:"uid" binding:"required"`           // 用户ID
		ContractCode string                    `json:"contract_code" binding:"required"` // 合约代码
		Amount       decimal.Decimal           `json:"amount"`
		MarginMode   futuresassetpb.MarginMode `json:"margin_mode"`
		PosSide      futuresassetpb.PosSide    `json:"pos_side"`
		AwardOpIds   []string                  `json:"award_op_ids"`
	}
	HoldModeParam struct {
		UID          string                      `json:"uid" binding:"required"` // 用户ID
		PositionMode futuresassetpb.PositionMode `json:"position_mode"`          // 保证金模式 1.双向持仓 2.单向持仓
	}
	LeverageMarginAdAdjust struct {
		CommonParam
		MarginMode futuresassetpb.MarginMode `json:"margin_mode"`
		Leverage   int                       `json:"leverage"`
		LLeverage  int                       `json:"l_leverage"` // 逐仓 多仓杠杆倍数
		SLeverage  int                       `json:"s_leverage"` // 逐仓 空仓杠杆倍数
		BLeverage  int                       `json:"b_leverage"` // 单向持仓 逐仓杠杆倍数
	}
	HoldModeRes struct {
		UID          string                      `json:"uid"`           // 用户ID
		PositionMode futuresassetpb.PositionMode `json:"position_mode"` // positionMode
	}
	JoinMarginRes struct {
		UID       string                   `json:"uid"`        // 用户ID
		AssetMode futuresassetpb.AssetMode `json:"asset_mode"` // 保证金模式 1.单币保证金 2.联合保证金
	}
	UpdateAssetModeParam struct {
		UID       string                   `json:"uid" binding:"required"` // 用户ID
		AssetMode futuresassetpb.AssetMode `json:"asset_mode"`
	}
	OrderConfirm struct {
		Limit    int `json:"limit"`     // 限价二次确认
		Market   int `json:"market"`    // 市价二次确认
		Trigger  int `json:"trigger"`   // 止盈止损二次确认
		PostOnly int `json:"post_only"` // 只做Maker二次确认
		Backhand int `json:"backhand"`  // 反手二次确认
	}
	OrderConfirmRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfirm
	}
	UpdateOrderConfirmParam struct {
		UID string `json:"uid" binding:"required"` // 用户ID
		OrderConfirm
	}
	OrderConfig struct {
		RebornCard int `json:"reborn_card"` // 复活卡
		Trial      int `json:"trial"`       // 体验金
	}
	OrderConfigRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfig
	}
	UpdateOrderConfigParam struct {
		UID string `json:"uid" binding:"required"` // 用户ID
		OrderConfig
	}
	AdjustCrossParam struct {
		UID string `json:"uid" binding:"required"` // 用户ID
	}
	UpdateMarginModeParam struct {
		UID          string                    `json:"uid" binding:"required"`           // 用户ID
		ContractCode string                    `json:"contract_code" binding:"required"` // 合约代码
		MarginMode   futuresassetpb.MarginMode `json:"margin_mode"`
	}
	UpdateLeverageParam struct {
		UID          string `json:"uid" binding:"required"`           // 用户ID
		ContractCode string `json:"contract_code" binding:"required"` // 合约代码
		Leverage     int    `json:"leverage"`                         // 全仓杠杆倍数
		LLeverage    int    `json:"l_leverage"`                       // 逐仓 多仓杠杆倍数
		SLeverage    int    `json:"s_leverage"`                       // 逐仓 空仓杠杆倍数
		BLeverage    int    `json:"b_leverage"`                       // 单向持仓 逐仓杠杆倍数
	}
)

func (p *UIDParam) ToUseCase() *repository.UIDParam {
	return &repository.UIDParam{
		UID: p.UID,
	}
}

type CommonParam struct {
	UID          string `json:"uid" binding:"required"` // 用户ID
	ContractCode string `json:"contract_code"`          // 合约代码
}

func (p *CommonParam) ToUseCase() *repository.CommonParam {
	return &repository.CommonParam{
		UID:          p.UID,
		ContractCode: p.ContractCode,
	}
}

func (p *UpdateAssetModeParam) ToUseCase() *repository.ChangeJoinMarginParam {
	return &repository.ChangeJoinMarginParam{
		UID:       p.UID,
		AssetMode: p.AssetMode,
	}
}

func (p *HoldModeParam) ToUseCase() *repository.HoldModeParam {
	return &repository.HoldModeParam{
		UID:          p.UID,
		PositionMode: p.PositionMode,
	}
}

func (p *MarginParam) ToUseCase() *repository.MarginParam {
	return &repository.MarginParam{
		UID:          p.UID,
		ContractCode: p.ContractCode,
		Amount:       p.Amount,
		MarginMode:   p.MarginMode,
		PosSide:      p.PosSide,
		AwardOpIds:   p.AwardOpIds,
	}
}

func (p *UpdateMarginModeParam) ToUseCase() *repository.MarginAdjust {
	return &repository.MarginAdjust{
		UID:          p.UID,
		ContractCode: p.ContractCode,
		MarginMode:   p.MarginMode,
	}
}

func (p *AdjustCrossParam) ToUseCase() *repository.AdjustCrossParam {
	return &repository.AdjustCrossParam{
		UID: p.UID,
	}
}

func (p *LeverageMarginAdAdjust) ToUseCase() *repository.LeverageMarginAdAdjust {
	return &repository.LeverageMarginAdAdjust{
		UID:          p.UID,
		ContractCode: p.ContractCode,
		MarginMode:   p.MarginMode,
		Leverage:     p.Leverage,
		LLeverage:    p.LLeverage,
		SLeverage:    p.SLeverage,
		BLeverage:    p.BLeverage,
	}
}

func (p *UpdateLeverageParam) ToUseCase() *repository.LeverageAdjust {
	return &repository.LeverageAdjust{
		UID:          p.UID,
		ContractCode: p.ContractCode,
		Leverage:     p.Leverage,
		LLeverage:    p.LLeverage,
		SLeverage:    p.SLeverage,
		BLeverage:    p.BLeverage,
	}
}

func (p *UpdateOrderConfirmParam) ToUseCase() *repository.ChangeOrderConfirmParam {
	return &repository.ChangeOrderConfirmParam{
		UID: p.UID,
		OrderConfirm: repository.OrderConfirm{
			Limit:    p.Limit,
			Market:   p.Market,
			Trigger:  p.Trigger,
			PostOnly: p.PostOnly,
			Backhand: p.Backhand,
		},
	}
}

func (p *UpdateOrderConfigParam) ToUseCase() *repository.ChangeOrderConfigParam {
	return &repository.ChangeOrderConfigParam{
		UID: p.UID,
		OrderConfig: repository.OrderConfig{
			RebornCard: p.RebornCard,
			Trial:      p.Trial,
		},
	}
}
