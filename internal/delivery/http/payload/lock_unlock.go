package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"

	"github.com/shopspring/decimal"
)

type (
	LockAssetOrder struct {
		OrderID         string          `json:"order_id"`         // 委托单ID
		Amount          decimal.Decimal `json:"amount"`           // 数量
		OrderTime       int64           `json:"order_time"`       // 委托创建时间
		LiquidationType int             `json:"liquidation_type"` // 强平类型
		AwardIds        []string        `json:"award_ids"`        // 体验金券ID
		AwardUsed       bool            `json:"award_used"`       // 体验金是否使用
	}
	BatchLockAssetParam struct {
		UID          string                      `json:"uid"`           // 用户ID
		UserType     futuresassetpb.UserType     `json:"user_type"`     // 用户类型
		Symbol       string                      `json:"symbol"`        // 币对
		Currency     string                      `json:"currency"`      // 计价币种
		Leverage     int32                       `json:"leverage"`      // 杠杆倍数
		IsInnerCall  int32                       `json:"is_inner_call"` // 是否为服务内部调用
		PosSide      futuresassetpb.PosSide      `json:"pos_side"`      // 持仓方向
		MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 仓位模式 (1:全仓 2:逐仓)
		PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 持仓模式
		Orders       []*LockAssetOrder           `json:"orders"`        // 订单列表
		IsTrial      bool                        `json:"is_trial"`      // 是否体验金冻结
	}
	LockReply struct {
		OrderId      string          `json:"order_id"`      // 委托单ID
		Amount       decimal.Decimal `json:"amount"`        // 数量
		FrozenMargin decimal.Decimal `json:"frozen_margin"` // 冻结数量
	}
)

func (p BatchLockAssetParam) ToUseCaseParam() *usecase.BatchLockAssetParam {
	orders := make([]*usecase.LockAssetOrder, 0, len(p.Orders))
	for _, v := range p.Orders {
		orders = append(orders, &usecase.LockAssetOrder{
			OrderID:         v.OrderID,
			Amount:          v.Amount,
			OrderTime:       v.OrderTime,
			LiquidationType: v.LiquidationType,
			AwardIds:        v.AwardIds,
			AwardUsed:       v.AwardUsed,
		})
	}

	return &usecase.BatchLockAssetParam{
		UID:          p.UID,
		UserType:     p.UserType,
		Symbol:       p.Symbol,
		Currency:     p.Currency,
		Leverage:     p.Leverage,
		IsInnerCall:  p.IsInnerCall,
		PosSide:      p.PosSide,
		MarginMode:   p.MarginMode,
		PositionMode: p.PositionMode,
		Orders:       orders,
		IsTrial:      p.IsTrial,
	}
}

// func (p LockParam) ContractCode() string {
// 	return util.ContractCode(p.Base, p.Quote)
// }

// // GetLeverage 获取对应的杠杆配置
// func (p LockParam) GetLeverage(l repository.Leverage) int {
// 	return l.GetLeverage(p.PositionMode, p.MarginMode, p.PosSide, p.Side)
// }

// // Value 获取价值
// func (p LockParam) Value() decimal.Decimal {
// 	return p.Price.Mul(p.Amount)
// }

// LockRes lock reply data.
type LockRes struct {
	Code           domain.Code                 `json:"code"`
	Msg            string                      `json:"msg"`
	Amount         decimal.Decimal             `json:"amount"`
	FrozenMargin   decimal.Decimal             `json:"frozen_margin"`
	PositionMode   futuresassetpb.PositionMode `json:"hold_mode"`
	OrderId        string                      `json:"order_id"`
	JoinMarginRate decimal.Decimal             `json:"rate"`
	HaveTrial      int                         `json:"have_trial"`
}
