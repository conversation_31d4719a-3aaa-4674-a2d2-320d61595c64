package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type AssetParam struct {
	UserIds  []string `json:"userIds" binding:"required"`
	Currency string   `json:"currency" binding:"required"`
}

func (p *AssetParam) ToUseCase() usecase.AssetParam {
	return usecase.AssetParam{
		UserIds:  p.UserIds,
		Currency: p.Currency,
	}
}

// 批量冻结参数
type (
	BatchLock struct {
		AccountType  string            `json:"accountType"`
		UID          string            `json:"uid" binding:"required"`
		UserType     int               `json:"userType"`
		MarginMode   domain.MarginMode `json:"marginMode"`
		Base         string            `json:"base" binding:"required"`
		Quote        string            `json:"quote" binding:"required"`
		Side         int32             `json:"side" binding:"required"`
		PosSide      int32             `json:"pos_side" binding:"required"`
		OperateTime  int64             `json:"operateTime"`
		Platform     string            `json:"platform"`      // 来源 (WEB,APP,H5)
		PositionMode int32             `json:"position_mode"` // 持仓模式 1.双向 2.单向 委托接口需要加参数
		Orders       []BatchLockOrder  `json:"orders"`
	}
	BatchLockOrder struct {
		OrderId   string          `json:"orderId"`
		OrderType int32           `json:"orderType"` // 委托类型 (1:限价 2:市价 3-限价止盈 4-市价止盈 5-限价止损 6-市价止损)
		Side      int32           `json:"side"`      // 委托单方向 (1:买 2:卖)
		Price     decimal.Decimal `json:"price"`
		Amount    decimal.Decimal `json:"amount"`
		Leverage  int             `json:"leverage"`
	}

	BatchLockItem struct {
		Code         domain.Code     `json:"code"`
		OrderId      string          `json:"orderId"`
		Amount       decimal.Decimal `json:"amount"`       // 市价开仓或止盈止损平仓,返回实际开仓或平仓的数量(需考虑不足的情况)
		FrozenMargin decimal.Decimal `json:"frozenMargin"` // 开仓时-返回实际冻结的保证金数量
		HaveTrial    int             `json:"haveTrial"`
	}
	BatchLockReply struct {
		Code   int             `json:"code"`
		Msg    string          `json:"msg"`
		Orders []BatchLockItem `json:"orders"`
	}
)

func (p BatchLock) IsClose() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Short) ||
		(p.Side == domain.Sell && p.PosSide == domain.Long)
}

func (p BatchLock) IsOpen() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Long) ||
		(p.Side == domain.Sell && p.PosSide == domain.Short)
}

// 批量解冻参数
type (
	UnLockParam struct {
		UID             string                      `json:"uid"`              // 用户ID
		UserType        futuresassetpb.UserType     `json:"user_type"`        // 用户类型
		OrderId         string                      `json:"order_id"`         // 委托单ID
		Symbol          string                      `json:"symbol"`           // 币对
		Currency        string                      `json:"currency"`         // 计价币种
		Amount          decimal.Decimal             `json:"amount"`           // 数量
		MarginMode      futuresassetpb.MarginMode   `json:"margin_mode"`      // 仓位模式 (1:全仓 2:逐仓)
		IsInnerCall     int32                       `json:"is_inner_call"`    // 是否为服务内部调用下单(爆仓) 1:内部服务下单
		PositionMode    futuresassetpb.PositionMode `json:"position_mode"`    // 持仓模式 (1.双向 2.单向)
		LiquidationType int32                       `json:"liquidation_type"` // 强平类型
		AwardOpIds      []string                    `json:"award_op_ids"`     // 体验金券ID
		TrialIsEnd      bool                        `json:"trial_is_end"`     // 体验金是否成交
	}

	// BatchUnlockParam 批量解锁用户保证金
	BatchUnlockParam struct {
		UID          string                      `json:"uid"`           // 用户ID
		UserType     futuresassetpb.UserType     `json:"user_type"`     // 用户类型
		Symbol       string                      `json:"symbol"`        // 币对
		Currency     string                      `json:"currency"`      // 计价币种
		MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 仓位模式 (1:全仓 2:逐仓)
		PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 持仓模式 (1.双向 2.单向)

		Orders  []*UnLockParam `json:"orders"`
		IsTrial bool           `json:"is_trial"` // 是否体验金冻结
	}
)

func (p *BatchUnlockParam) ToUseCaseParam() *usecase.BatchUnlockParam {
	return &usecase.BatchUnlockParam{
		UID:          p.UID,
		UserType:     p.UserType,
		Symbol:       p.Symbol,
		Currency:     p.Currency,
		MarginMode:   p.MarginMode,
		PositionMode: p.PositionMode,
		Orders:       []*usecase.UnLockParam{},
		IsTrial:      p.IsTrial,
	}
}
