package http

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"yt.com/backend/common.git/transport/http/health"
	"yt.com/backend/common.git/transport/http/middleware"

	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

const (
	cronInterval = 10 * time.Minute
)

func (a *Server) registerHTTPRouter(_ context.Context, healths []health.CheckConfig) error {
	ginMode := gin.ReleaseMode
	if a.conf.Env.Debug {
		ginMode = gin.DebugMode
	}

	gin.SetMode(ginMode)
	a.httpRouter = gin.New()

	// 初始化otel middleware內容
	otelHostNameTag := a.conf.Env.ServiceName
	podName := os.Getenv("POD_NAME")
	if podName != "" {
		otelHostNameTag = podName
	}

	otelginMiddleware := otelgin.Middleware(
		otelHostNameTag,
		otelgin.WithSpanNameFormatter(func(r *http.Request) string {
			return fmt.Sprintf("%s %s", r.Method, r.URL.Path)
		}),
	)

	logger := log.StandardLogger()
	a.httpRouter.Use(
		otelginMiddleware,
		middleware.GinRecover(logger),
		middleware.GinTimeout(logger, middleware.DefaultTimeout),
		middleware.GinRequestID(),
	)

	h := health.New(logger, health.WithChecks(healths...))
	a.httpRouter.GET(health.DefaultHealthEndpoint, gin.WrapH(h.Handler()))

	api := a.httpRouter.Group("")
	if err := a.apiRouter(api); err != nil {
		return err
	}

	cronAPI := a.httpRouter.Group("/cron", middleware.GinTimeout(logger, cronInterval))
	if err := a.cronRouter(cronAPI); err != nil {
		return err
	}

	return nil
}

// apiRouter registers the API handlers for the server.
//
// The API handlers are grouped by functionality, such as batch operations,
// option operations, asset operations, position operations, user configuration
// operations, and data query operations.
//
// The API endpoints are defined in the comments above each handler.
//
// The function returns an error if any of the handlers fail to register.
func (a *Server) apiRouter(api *gin.RouterGroup) error {
	// 批量接口
	err := a.container.Invoke(func(param BatchHandlerParam) {
		batch := newBatchHandler(param)

		api.POST("/perp/v1/batch/asset", batch.userAsset) // 批量获取用户资产
		// api.POST("/perp/v1/batch/lock", batch.batchLockAsset)     // 批量冻结
		// api.POST("/perp/v1/batch/unlock", batch.batchUnlockAsset) // 批量解冻
	})
	if err != nil {
		return fmt.Errorf("batch handler param fail :%w", err)
	}

	// 期权接口
	err = a.container.Invoke(func(param OptionHandlerParam) {
		option := newOptionHandler(param)

		api.POST("/option/v1/init", option.initOption)
		api.POST("/option/v1/asset", option.asset)
		// api.POST("/option/v1/trade", option.trade)
		// api.POST("/option/v1/exercise", option.exercise)
		// api.POST("/option/v1/demo-asset", option.demoAsset)
		api.POST("/option/v1/profit/loss/record", option.profitLossRecord) // 获取盈亏记录
		api.POST("/option/v1/bill", option.billOption)                     // 获取用户的资金流水
	})
	if err != nil {
		return fmt.Errorf("option handler param fail :%w", err)
	}

	// 资产接口
	err = a.container.Invoke(func(param AssetHandlerParam) {
		asset := newAssetHandler(param)
		// logger := log.StandardLogger()

		api.POST("/perp/v1/user/asset", asset.GetUserAsset)                 // 根据用户ID获取用户资产
		api.POST("/perp/v1/total/balance", asset.GetTotalBalance)           // 获取用户衍生品账户总资产折合
		api.POST("/perp/v1/asset-detail", asset.AssetDetail)                // 获取用户资产汇总
		api.POST("/perp/v1/max/transfer", asset.MaxTransfer)                // 获取用户最大可划转数量
		api.POST("/perp/v1/account/max/transfer", asset.AccountMaxTransfer) // 获取用户账号最大可划转数量
		// api.POST("/perp/v1/incr/:sign", middleware.Signature(logger), asset.IncrAsset) // 增加资产
		// api.POST("/perp/v1/decr/:sign", middleware.Signature(logger), asset.DecrAsset) // 减少资产
		// api.POST("/perp/v1/transfer/inner", asset.InnerTransfer)                       // 资金划转
		api.POST("/perp/v1/asset", asset.UserAsset)         // 获取用户资产 新版可能需要废弃
		api.POST("/perp/v1/trial-add", asset.AddTrialAsset) // 手动增加体验金
		// api.POST("/perp/v1/trial-recycle", asset.RecycleTrialAsset) // 手动回收体验金
		api.POST("/perp/v1/sum/balance", asset.SumUserTotalAsset) // 统计所有用户总资产
	})
	if err != nil {
		return fmt.Errorf("asset handler param fail :%w", err)
	}

	// 仓位接口
	err = a.container.Invoke(func(param PositionHandlerParam) {
		position := newPositionHandler(param)

		api.POST("/perp/v1/is-bursting", position.UserIsBurst) // 用户是否爆仓中
		api.POST("/perp/v1/pos", position.UserPos)             // 获取用户持仓
		// api.POST("/perp/v1/reborn/send", position.SendRebornCard) // 发放重生卡
		// todo 测试时使用
		api.POST("/perp/v1/test/settlement", position.TestSettlement) // 触发资金费结算
	})
	if err != nil {
		return fmt.Errorf("position handler param fail :%w", err)
	}

	// 用户配置项接口
	err = a.container.Invoke(func(param UserHandlerParam) {
		user := newUserHandler(param)

		// 	api.POST("/perp/v1/init", user.InitSwap)                               // 初始化合约账户
		// 	api.POST("/perp/v1/adjust/leverage/margin", user.LeverageMarginAdjust) // 调整仓位模式和杠杠倍数
		// 	api.POST("/perp/v1/mark/price", user.MarkPrice)                        // 获取缓存的标记价格
		api.GET("/perp/v1/user/basic-config", user.UserBasicConfig) // ✅获取用户合约基础配置
		api.GET("/perp/v1/user/symbol-config", user.UserLeverage)   // ✅获取用户币对配置 (杠杆倍数,保证金模式)

		api.POST("/perp/v1/position/margin", user.UpdatePositionMargin)  // 调整逐仓仓位保证金
		api.POST("/perp/v1/user/order-config", user.UpdateOrderConfig)   // ✅切换交易设置
		api.POST("/perp/v1/user/order-confirm", user.UpdateOrderConfirm) // ✅修改下单确认模式
		api.POST("/perp/v1/user/leverage", user.UpdateLeverage)          // ✅调整杠杠倍数
		api.POST("/perp/v1/user/cross", user.UpdateCrossMarginMode)      // ✅切换混合保证金之前 一键切换全仓
		api.POST("/perp/v1/user/asset-mode", user.UpdateAssetMode)       // ✅调整资产模式 (单币种, 联合保证金)
		api.POST("/perp/v1/user/position-mode", user.UpdatePositionMode) // ✅调整持仓模式 (单向, 双向)
		api.POST("/perp/v1/user/margin-mode", user.UpdateMarginMode)     // ✅切换保证模式 (逐仓, 全仓)
	})
	if err != nil {
		return fmt.Errorf("user handler param fail :%w", err)
	}

	// 数据查询接口
	err = a.container.Invoke(func(param DataHandlerParam) {
		data := newDataHandler(param)

		api.POST("/perp/v1/bill/asset", data.BillAsset) // 获取用户的资金流水
		api.POST("/perp/v1/burst/list", data.BurstList) // 爆仓查询列表
		api.POST("/perp/v1/burst/stat", data.BurstStat) // 爆仓统计查询
		// api.POST("/perp/v1/index/price/info", data.IndexPriceInfo) // 指数价格详情
		// api.POST("/perp/v1/index/price/list", data.IndexPriceList) // 指数价格列表

		api.POST("/perp/v1/trial/asset/list", data.GetTrialAssetList) // 查询用户体验金记录
		api.POST("/perp/v1/no/invalid/trial", data.GetNoInvalidTrial) // 获取用户未失效的体验金

		api.POST("/perp/v1/pos/list", data.QueryUserPos)         // 运营后台-用户持仓查询
		api.POST("/perp/v1/pos/info", data.PosInfo)              // 运营后台-用户持仓查询-仓位详情
		api.POST("/perp/v1/plat/pos/list", data.PlatPosList)     // 获取合约持仓列表
		api.POST("/perp/v1/plat/pos/detail", data.PlatPosDetail) // 获取合约持仓详情

		api.POST("/perp/v1/funding/list", data.FundingList)            // 资金费用查询
		api.POST("/perp/v1/fund/rate/list", data.FundRateList)         // 资金费率列表
		api.POST("/perp/v1/fund/rate/list/mobile", data.FundRateList)  // 移动端资金费率列表
		api.POST("/perp/v1/get/fund/rate", data.LatestFundRate)        // 获取当前最新的资金费率
		api.POST("/perp/v1/fund/rate/all", data.FundRateAll)           // 获取平台实时资金费率
		api.POST("/perp/v1/user/data-statistics", data.UserStatistics) // 获取所有用户合约数据统计
		// api.POST("/perp/v1/user/win/rate", data.UserWinRate)           // 获取所有用户胜率数据
		// api.POST("/perp/v1/user/times", data.UserOpenCloseTimes)       // 获取用户开仓和平仓的次数统计

		api.POST("/perp/v1/profit/loss/record", data.ProfitLossRecord) // 获取盈亏记录
		api.POST("/perp/v1/get/total/subsidy", data.GetTotalSubsidy)   // 获取平台穿仓补贴总计
		api.POST("/perp/v1/find/subsidy", data.FindSubsidy)            // 获取穿仓补贴列表

		api.POST("/perp/v1/get/base/num", data.GetBaseNum) // 获取合约基差计算周期
		// api.POST("/perp/v1/set/base/num", data.SetBaseNum) // 更新合约基差计算周期

		api.POST("/perp/v1/plat/data", data.GetPlatData) // 获取平台当前仓位汇总信息

		// api.POST("/perp/v1/user/hold-pos", data.FetchUserHoldModeAndPos)             // 获取用户持仓模式及仓位数量(撮合专用)
		api.POST("/perp/v1/trial/asset/summary-list", data.GetTrialAssetSummaryList) // 查询用户体验金汇总记录
		api.POST("/perp/v1/trial/asset/detail-list", data.GetTrialAssetDetailList)   // 查询用户体验金明细记录
		api.POST("/perp/v1/trial/asset/last-time", data.GetTrialAssetLastUpdate)     // 查询体验金券最后更新时间
	})
	if err != nil {
		return fmt.Errorf("data handler param fail :%w", err)
	}

	return nil
}

func (a *Server) cronRouter(api *gin.RouterGroup) error {
	err := a.container.Invoke(func(param CronParam) {
		handler := newCronHandler(param)

		api.POST("/cron/v1/transaction/hour", handler.transactionHour)
	})
	if err != nil {
		return fmt.Errorf("newCronHandler fail :%w", err)
	}

	return nil
}
