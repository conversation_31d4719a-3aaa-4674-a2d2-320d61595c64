package cache

import (
	"fmt"
	"strings"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type TaskType string

// contract asset redis key
const (
	Prefix         = "fasset:"  // fasset:{uid}
	PosFlag        = "pos"      // pos flag
	LeverageSuffix = "leverage" // leverage suffix

	LongPosSuffix  = ":pos:long"  // swap asset long pos profit suffix
	ShortPosSuffix = ":pos:short" // swap asset short pos profit suffix
	BothPosSuffix  = ":pos:both"  // swap asset both pos profit suffix
)

type (
	SwapBurstTask struct {
		Type     TaskType `json:"type"`
		Data     []byte   `json:"data"`
		FuncName string   `json:"func_name"`
	}
	LiquidationInfo struct {
		BurstId              string                 `json:"burstId"`
		UID                  string                 `json:"uid"`
		PosId                string                 `json:"posId"`
		MarginMode           domain.MarginMode      `json:"marginMode"`
		PosSide              int32                  `json:"posSide"`
		LiquidationType      domain.LiquidationType `json:"liquidationType"`
		CurrentLevel         int                    `json:"currentLevel"`
		TargetLevel          int                    `json:"targetLevel	"`
		TargetLimit          decimal.Decimal        `json:"targetLimit"`
		CancelType           domain.CancelType      `json:"cancelType"`
		CollapsePrice        decimal.Decimal        `json:"collapsePrice"`        // 破产价格
		CollapsePriceFormula string                 `json:"collapsePriceFormula"` // 破产价格公式
		BurstTime            int64                  `json:"burstTime"`
		IsTrialPos           bool                   `json:"isTrialPos"` // 是否体验金仓位
	}
)

func GetContractLastPriceRedisChannel(accountType string, base string, quote string) string {
	return fmt.Sprintf("c_%s:last_price_channel:%v_%v", accountType, base, quote)
}

func GetContractMarkPriceRedisChannel(base, quote string) string {
	return fmt.Sprintf("contract_swap_mark_price_channel_%s_%s", strings.ToLower(base), strings.ToLower(quote))
}

func GetCloseAllUserPosListRedisKey(base, quote string) string {
	return fmt.Sprintf("fasset:burst:%s-%s:close_all_pos_list", base, quote)
}
